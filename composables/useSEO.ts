export const useSEO = () => {
  const route = useRoute()
  const baseUrl = 'https://geminigen.ai'

  /**
   * Set canonical URL for the current page
   */
  const setCanonicalUrl = (customPath?: string) => {
    const canonicalPath = customPath || route.path
    const canonicalUrl = `${baseUrl}${canonicalPath}`

    useHead({
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl
        }
      ]
    })
  }

  /**
   * Set hreflang tags for multi-language support
   */
  const setHreflangTags = (languages: string[] = ['en', 'vi']) => {
    const currentPath = route.path
    const hreflangLinks = languages.map(lang => ({
      rel: 'alternate',
      hreflang: lang,
      href: `${baseUrl}${currentPath}?lang=${lang}`
    }))

    // Add x-default
    hreflangLinks.push({
      rel: 'alternate',
      hreflang: 'x-default',
      href: `${baseUrl}${currentPath}`
    })

    useHead({
      link: hreflangLinks
    })
  }

  /**
   * Set Open Graph image
   */
  const setOgImage = (imagePath: string) => {
    const imageUrl = imagePath.startsWith('http')
      ? imagePath
      : `${baseUrl}${imagePath}`

    useSeoMeta({
      ogImage: imageUrl,
      twitterImage: imageUrl
    })
  }

  /**
   * Set breadcrumb structured data
   */
  const setBreadcrumbs = (breadcrumbs: Array<{ name: string; url: string }>) => {
    const breadcrumbList = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': `${baseUrl}${item.url}`
      }))
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(breadcrumbList)
        }
      ]
    })
  }

  /**
   * Set FAQ structured data
   */
  const setFAQStructuredData = (faqs: Array<{ question: string; answer: string }>) => {
    const faqStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': faqs.map(faq => ({
        '@type': 'Question',
        'name': faq.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': faq.answer
        }
      }))
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(faqStructuredData)
        }
      ]
    })
  }

  /**
   * Set article structured data
   */
  const setArticleStructuredData = (article: {
    title: string
    description: string
    author: string
    datePublished: string
    dateModified?: string
    image?: string
  }) => {
    const articleStructuredData = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': article.title,
      'description': article.description,
      'author': {
        '@type': 'Person',
        'name': article.author
      },
      'publisher': {
        '@type': 'Organization',
        'name': 'GeminiGen AI',
        'logo': {
          '@type': 'ImageObject',
          'url': `${baseUrl}/logo.png`
        }
      },
      'datePublished': article.datePublished,
      'dateModified': article.dateModified || article.datePublished,
      'image': article.image ? `${baseUrl}${article.image}` : `${baseUrl}/og-image.png`
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(articleStructuredData)
        }
      ]
    })
  }

  /**
   * Set organization structured data
   */
  const setOrganizationStructuredData = () => {
    const organizationData = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': 'GeminiGen AI',
      'url': baseUrl,
      'logo': `${baseUrl}/logo.png`,
      'description': 'Advanced AI tools for video generation, speech synthesis, dialogue creation, and image generation.',
      'contactPoint': {
        '@type': 'ContactPoint',
        'contactType': 'Customer Service',
        'email': '<EMAIL>'
      },
      'sameAs': [
        // Add social media links when available
      ]
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(organizationData)
        }
      ]
    })
  }

  /**
   * Set website structured data
   */
  const setWebsiteStructuredData = () => {
    const websiteData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': 'GeminiGen AI',
      'url': baseUrl,
      'description': 'Advanced AI tools for video generation, speech synthesis, dialogue creation, and image generation.',
      'potentialAction': {
        '@type': 'SearchAction',
        'target': `${baseUrl}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(websiteData)
        }
      ]
    })
  }

  /**
   * Set product structured data
   */
  const setProductStructuredData = (product: {
    name: string
    description: string
    image?: string
    price?: string
    currency?: string
    category?: string
  }) => {
    const productData = {
      '@context': 'https://schema.org',
      '@type': 'Product',
      'name': product.name,
      'description': product.description,
      'image': product.image || `${baseUrl}/og-image.jpg`,
      'brand': {
        '@type': 'Brand',
        'name': 'GeminiGen AI'
      },
      'category': product.category || 'AI Software',
      'offers': product.price ? {
        '@type': 'Offer',
        'price': product.price,
        'priceCurrency': product.currency || 'USD',
        'availability': 'https://schema.org/InStock'
      } : undefined
    }

    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(productData)
        }
      ]
    })
  }

  /**
   * Set comprehensive page SEO
   */
  const setPageSEO = (options: {
    title?: string
    description?: string
    keywords?: string
    image?: string
    type?: 'website' | 'product' | 'article'
    noindex?: boolean
  } = {}) => {
    const {
      title = 'GeminiGen AI - Advanced AI Tools for Content Creation',
      description = 'Create amazing videos, speech, and images with our advanced AI tools. Join thousands of creators using GeminiGen AI.',
      keywords = 'AI video generation, AI speech synthesis, AI dialogue generation, AI image generation',
      image = `${baseUrl}/og-image.jpg`,
      type = 'website',
      noindex = false
    } = options

    const currentUrl = `${baseUrl}${route.path}`

    useSeoMeta({
      title,
      description,
      keywords,
      ogTitle: title,
      ogDescription: description,
      ogImage: image,
      ogUrl: currentUrl,
      ogType: type,
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: image,
      robots: noindex ? 'noindex, nofollow' : 'index, follow'
    })

    setCanonicalUrl()
  }

  return {
    setCanonicalUrl,
    setHreflangTags,
    setOgImage,
    setBreadcrumbs,
    setFAQStructuredData,
    setArticleStructuredData,
    setOrganizationStructuredData,
    setWebsiteStructuredData,
    setProductStructuredData,
    setPageSEO
  }
}
