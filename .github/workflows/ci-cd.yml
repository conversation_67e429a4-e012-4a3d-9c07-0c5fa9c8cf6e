name: Reusable CI/CD Workflow

on:
  workflow_call:
    inputs:
      service_name:
        description: "Name of the service"
        required: true
        type: string
      env_code:
        description: "Environment code (dev, stg, prod)"
        required: true
        type: string
      environment:
        description: "Environment name (Develop, Staging, Production)"
        required: true
        type: string
      git_branch:
        description: "Branch name (develop, main, …)"
        required: true
        type: string

jobs:
  build:
    name: Build & Deploy ${{ inputs.service_name }}
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}

    env:
      SERVICE_NAME: ${{ inputs.service_name }}
      ECR_URL: 600696936390.dkr.ecr.eu-central-1.amazonaws.com
      AWS_REGION: eu-central-1
      ENV_CODE: ${{ inputs.env_code }}
      GIT_BRANCH: ${{ inputs.git_branch }}
      

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      
      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup File Enviroment
        run: |
          cat <<'EOF' > .env
          ${{ vars.ENV_FILE_CONTENT }}
          EOF
          cat .env

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build project (Nuxt generate)
        run: pnpm run generate

      - name: Publish to Cloudflare Pages (via Wrangler)
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ vars.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy .output/public --project-name=frontend-geminigen-${{env.ENV_CODE}}
        env:
          npm_config_legacy_peer_deps: true

      # Notify success
      - name: Notify Slack (Success)
        if: ${{ success() }}
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,commit,author,ref,workflow,pullRequest
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      # Notify fail + mention user
      - name: Notify Slack (Fail)
        if: ${{ failure() }}
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,commit,author,ref,workflow,pullRequest
          text: "❌ Build failed! ${{ vars.SLACK_MENTIONS }} please check 🚨"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}